{"version": 3, "file": "number.js", "sourceRoot": "", "sources": ["../../src.ts/coders/number.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,SAAS,EAAgB,MAAM,0BAA0B,CAAC;AACnE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,0BAA0B,CAAC;AAE9E,OAAO,EAAE,KAAK,EAAkB,MAAM,kBAAkB,CAAC;AAEzD,MAAM,OAAO,WAAY,SAAQ,KAAK;IAIlC,YAAY,IAAY,EAAE,MAAe,EAAE,SAAiB;QACxD,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAA,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;QACrD,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAEpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED,YAAY;QACR,OAAO,CAAC,CAAC;IACb,CAAC;IAED,MAAM,CAAC,MAAc,EAAE,KAAmB;QACtC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE9B,qCAAqC;QACrC,IAAI,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QACxD,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE;gBACxD,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;aAClD;SACJ;aAAM,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;YAC7D,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;SAClD;QAED,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QAEhD,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;SAC7D;QAED,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,MAAc;QACjB,IAAI,KAAK,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QAEnD,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;SACzC;QAED,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC;CACJ"}