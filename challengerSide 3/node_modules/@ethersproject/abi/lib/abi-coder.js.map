{"version": 3, "file": "abi-coder.js", "sourceRoot": "", "sources": ["../src.ts/abi-coder.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;AAEb,mEAAmE;AAEnE,8CAA2D;AAC3D,wDAA2D;AAE3D,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,0DAAwE;AACxE,4CAAgD;AAChD,wCAA4C;AAC5C,4CAAgD;AAChD,wCAA4C;AAC5C,oDAAuD;AACvD,sCAA0C;AAC1C,0CAA8C;AAC9C,0CAA8C;AAC9C,wCAA4C;AAE5C,yCAAwC;AAGxC,IAAM,cAAc,GAAG,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAC;AACrD,IAAM,eAAe,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAKxD;IAGI,kBAAY,UAAuB;QAC/B,IAAA,2BAAc,EAAC,IAAI,EAAE,YAAY,EAAE,UAAU,IAAI,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED,4BAAS,GAAT,UAAU,KAAgB;QAA1B,iBA0CC;QAxCG,QAAQ,KAAK,CAAC,QAAQ,EAAE;YACpB,KAAK,SAAS;gBACV,OAAO,IAAI,sBAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACxC,KAAK,MAAM;gBACP,OAAO,IAAI,sBAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACxC,KAAK,QAAQ;gBACT,OAAO,IAAI,oBAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACvC,KAAK,OAAO;gBACR,OAAO,IAAI,kBAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACtC,KAAK,OAAO;gBACR,OAAO,IAAI,kBAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YAC9F,KAAK,OAAO;gBACR,OAAO,IAAI,kBAAU,CAAC,CAAC,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAC,SAAS;oBACzD,OAAO,KAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBACrC,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACpB,KAAK,EAAE;gBACH,OAAO,IAAI,gBAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACxC;QAED,cAAc;QACd,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAC9C,IAAI,KAAK,EAAE;YACP,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC;YACvC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC9C,MAAM,CAAC,kBAAkB,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;aACpF;YACD,OAAO,IAAI,oBAAW,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;SACtE;QAED,cAAc;QACd,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACzC,IAAI,KAAK,EAAE;YACP,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,GAAG,EAAE,EAAE;gBACzB,MAAM,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;aACrE;YACD,OAAO,IAAI,6BAAe,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;SAChD;QAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,cAAc,EAAE,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IACzE,CAAC;IAED,+BAAY,GAAZ,cAAyB,OAAO,EAAE,CAAC,CAAC,CAAC;IAErC,6BAAU,GAAV,UAAW,IAAgB,EAAE,UAAoB;QAC7C,OAAO,IAAI,uBAAM,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAC9E,CAAC;IAED,6BAAU,GAAV;QACI,OAAO,IAAI,uBAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,kCAAe,GAAf,UAAgB,KAAwC;QAAxD,iBAIC;QAHG,IAAM,MAAM,GAAiB,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,KAAI,CAAC,SAAS,CAAC,qBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAApC,CAAoC,CAAC,CAAC;QACvF,IAAM,KAAK,GAAG,IAAI,kBAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC1C,OAAO,KAAK,CAAC,YAAY,EAAE,CAAC;IAChC,CAAC;IAED,yBAAM,GAAN,UAAO,KAAwC,EAAE,MAA0B;QAA3E,iBAcC;QAbG,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE;YAChC,MAAM,CAAC,UAAU,CAAC,8BAA8B,EAAE,eAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;gBAC9E,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE;gBACrD,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;aAC1C,CAAC,CAAC;SACN;QAED,IAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,KAAI,CAAC,SAAS,CAAC,qBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAApC,CAAoC,CAAC,CAAC;QACzE,IAAM,KAAK,GAAG,CAAC,IAAI,kBAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;QAE5C,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7B,OAAO,MAAM,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,yBAAM,GAAN,UAAO,KAAwC,EAAE,IAAe,EAAE,KAAe;QAAjF,iBAIC;QAHG,IAAM,MAAM,GAAiB,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,KAAI,CAAC,SAAS,CAAC,qBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAApC,CAAoC,CAAC,CAAC;QACvF,IAAM,KAAK,GAAG,IAAI,kBAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC1C,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAA,gBAAQ,EAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAChE,CAAC;IACL,eAAC;AAAD,CAAC,AAxFD,IAwFC;AAxFY,4BAAQ;AA0FR,QAAA,eAAe,GAAa,IAAI,QAAQ,EAAE,CAAC"}