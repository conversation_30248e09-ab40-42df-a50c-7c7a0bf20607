{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,EAAE,SAAS,EAAe,MAAM,sBAAsB,CAAC;AAC9D,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,WAAW,EAAqC,MAAM,2BAA2B,CAAC;AACvG,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAC;AACzE,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAUnD,oBAAY,kBAAkB,GAAG;IAC7B,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,YAAY,CAAC;IAErB,QAAQ,CAAC,EAAE,YAAY,CAAC;IACxB,QAAQ,CAAC,EAAE,YAAY,CAAC;IAExB,IAAI,CAAC,EAAE,SAAS,CAAC;IACjB,KAAK,CAAC,EAAE,YAAY,CAAC;IACrB,OAAO,CAAC,EAAE,MAAM,CAAA;IAEhB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,UAAU,CAAC,EAAE,aAAa,CAAC;IAE3B,oBAAoB,CAAC,EAAE,YAAY,CAAC;IACpC,YAAY,CAAC,EAAE,YAAY,CAAC;IAE5B,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACjC,eAAe,CAAC,EAAE,OAAO,CAAC;CAC7B,CAAA;AAED,MAAM,WAAW,mBAAoB,SAAQ,WAAW;IACpD,IAAI,EAAE,MAAM,CAAC;IAGb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB,aAAa,EAAE,MAAM,CAAC;IAGtB,IAAI,EAAE,MAAM,CAAC;IAGb,GAAG,CAAC,EAAE,MAAM,CAAC;IAGb,IAAI,EAAE,CAAC,aAAa,CAAC,EAAE,MAAM,KAAK,OAAO,CAAC,kBAAkB,CAAC,CAAA;CAChE;AAED,oBAAY,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;AAEvC,MAAM,WAAW,MAAM;IACnB,IAAI,EAAE,MAAM,CAAC;IACb,UAAU,EAAE,MAAM,CAAC;IACnB,MAAM,EAAE,MAAM,CAAC;IAEf,SAAS,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,MAAM,CAAC;IACd,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,SAAS,CAAC;IAEvB,QAAQ,EAAE,SAAS,CAAC;IACpB,OAAO,EAAE,SAAS,CAAC;IAEnB,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;IAElB,aAAa,CAAC,EAAE,IAAI,GAAG,SAAS,CAAC;CACpC;AAED,MAAM,WAAW,KAAM,SAAQ,MAAM;IACjC,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;CAC/B;AAED,MAAM,WAAW,qBAAsB,SAAQ,MAAM;IACjD,YAAY,EAAE,KAAK,CAAC,mBAAmB,CAAC,CAAC;CAC5C;AAGD,MAAM,WAAW,GAAG;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,MAAM,CAAC;IAClB,gBAAgB,EAAE,MAAM,CAAC;IAEzB,OAAO,EAAE,OAAO,CAAC;IAEjB,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;IAEb,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAEtB,eAAe,EAAE,MAAM,CAAC;IACxB,QAAQ,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,kBAAkB;IAC/B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,eAAe,EAAE,MAAM,CAAC;IACxB,gBAAgB,EAAE,MAAM,CAAC;IACzB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,SAAS,CAAC;IACnB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,eAAe,EAAE,MAAM,CAAC;IACxB,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;IACjB,WAAW,EAAE,MAAM,CAAC;IACpB,aAAa,EAAE,MAAM,CAAC;IACtB,iBAAiB,EAAE,SAAS,CAAC;IAC7B,iBAAiB,EAAE,SAAS,CAAC;IAC7B,SAAS,EAAE,OAAO,CAAC;IACnB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,CAAC,EAAE,MAAM,CAAA;CAClB;AAED,MAAM,WAAW,OAAO;IACpB,iBAAiB,EAAE,IAAI,GAAG,SAAS,CAAC;IACpC,YAAY,EAAE,IAAI,GAAG,SAAS,CAAC;IAC/B,oBAAoB,EAAE,IAAI,GAAG,SAAS,CAAC;IACvC,QAAQ,EAAE,IAAI,GAAG,SAAS,CAAC;CAC9B;AAED,MAAM,WAAW,WAAW;IACxB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC;CACjD;AAED,MAAM,WAAW,MAAO,SAAQ,WAAW;IACvC,SAAS,CAAC,EAAE,QAAQ,CAAC;IACrB,OAAO,CAAC,EAAE,QAAQ,CAAC;CACtB;AAED,MAAM,WAAW,iBAAkB,SAAQ,WAAW;IAClD,SAAS,CAAC,EAAE,MAAM,CAAC;CACtB;AAMD,8BAAsB,SAAU,SAAQ,WAAW;IAC/C,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;IAExB,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,CAAC;IAEhC,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,SAAS;CAGrD;AAED,qBAAa,cAAe,SAAQ,SAAS;IACzC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;IAE3B,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,CAAC;gBAEzB,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM;CAYjD;AAED,qBAAa,oBAAqB,SAAQ,SAAS;IAC/C,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IAEtB,QAAQ,CAAC,4BAA4B,CAAC,EAAE,OAAO,CAAC;gBAEpC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM;CAY5C;AAED,qBAAa,yBAA0B,SAAQ,SAAS;IACpD,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC;IAC5B,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;gBAEf,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM;CAgBrE;AAED,oBAAY,SAAS,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,WAAW,GAAG,SAAS,CAAC;AAEzF,oBAAY,QAAQ,GAAG,CAAC,GAAG,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC;AAIrD,8BAAsB,QAAS,YAAW,aAAa;IAGnD,QAAQ,CAAC,UAAU,IAAI,OAAO,CAAC,OAAO,CAAC;IAGvC,QAAQ,CAAC,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC;IAC1C,QAAQ,CAAC,WAAW,IAAI,OAAO,CAAC,SAAS,CAAC;IACpC,UAAU,IAAI,OAAO,CAAC,OAAO,CAAC;IAyBpC,QAAQ,CAAC,UAAU,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC;IACzH,QAAQ,CAAC,mBAAmB,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAC/H,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IACnH,QAAQ,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAGxK,QAAQ,CAAC,eAAe,CAAC,iBAAiB,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,mBAAmB,CAAC;IACnG,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,kBAAkB,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IACpH,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,UAAU,CAAC,kBAAkB,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC;IAGrF,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,EAAE,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC;IACtG,QAAQ,CAAC,wBAAwB,CAAC,mBAAmB,EAAE,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC,qBAAqB,CAAC;IACtI,QAAQ,CAAC,cAAc,CAAC,eAAe,EAAE,MAAM,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAC9E,QAAQ,CAAC,qBAAqB,CAAC,eAAe,EAAE,MAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAGpF,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAGrD,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAC5E,QAAQ,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAGjF,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,GAAG,QAAQ;IAC/D,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,GAAG,QAAQ;IACjE,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,GAAG,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO;IACjE,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,SAAS,GAAG,MAAM;IACrD,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;IAC1D,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,QAAQ;IACjE,QAAQ,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,SAAS,GAAG,QAAQ;IAG5D,WAAW,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,GAAG,QAAQ;IAK/D,cAAc,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,GAAG,QAAQ;IAKlE,QAAQ,CAAC,kBAAkB,CAAC,eAAe,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAE3H,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC;;IAO9B,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,QAAQ;CA2CnD"}