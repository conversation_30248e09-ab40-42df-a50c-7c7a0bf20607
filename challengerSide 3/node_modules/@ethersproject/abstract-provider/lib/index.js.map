{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,sDAAmE;AACnE,8CAA8D;AAE9D,wDAAuG;AAIvG,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AA8ClC,CAAC;AAkED,CAAC;AAuBF,qCAAqC;AACrC,0EAA0E;AAC1E,IAAI;AAEJ;IAAwC,6BAAW;IAAnD;;IAQA,CAAC;IAHU,qBAAW,GAAlB,UAAmB,KAAU;QACzB,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;IAC3C,CAAC;IACL,gBAAC;AAAD,CAAC,AARD,CAAwC,wBAAW,GAQlD;AARqB,8BAAS;AAU/B;IAAoC,kCAAS;IAKzC,wBAAY,SAAiB,EAAE,MAAe;QAA9C,iBAWC;QAVG,IAAI,CAAC,IAAA,mBAAW,EAAC,SAAS,EAAE,EAAE,CAAC,EAAE;YAC7B,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;SAC1E;QAED,QAAA,kBAAM;YACF,YAAY,EAAE,IAAI;YAClB,iBAAiB,EAAE,IAAI;YACvB,MAAM,EAAE,CAAC,MAAM,IAAI,CAAC,CAAC;YACrB,SAAS,EAAE,SAAS;SACvB,CAAC,SAAC;;IACP,CAAC;IACL,qBAAC;AAAD,CAAC,AAjBD,CAAoC,SAAS,GAiB5C;AAjBY,wCAAc;AAmB3B;IAA0C,wCAAS;IAK/C,8BAAY,IAAY,EAAE,MAAe;QAAzC,iBAWC;QAVG,IAAI,CAAC,IAAA,mBAAW,EAAC,IAAI,EAAE,EAAE,CAAC,EAAE;YACxB,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;SACvE;QAED,QAAA,kBAAM;YACF,YAAY,EAAE,IAAI;YAClB,uBAAuB,EAAE,IAAI;YAC7B,MAAM,EAAE,CAAC,MAAM,IAAI,CAAC,CAAC;YACrB,IAAI,EAAE,IAAI;SACb,CAAC,SAAC;;IACP,CAAC;IACL,2BAAC;AAAD,CAAC,AAjBD,CAA0C,SAAS,GAiBlD;AAjBY,oDAAoB;AAmBjC;IAA+C,6CAAS;IAIpD,mCAAY,UAAkB,EAAE,SAAiB,EAAE,MAAe;QAAlE,iBAeC;QAdG,IAAI,CAAC,IAAA,mBAAW,EAAC,UAAU,EAAE,EAAE,CAAC,EAAE;YAC9B,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;SACnF;QACD,IAAI,CAAC,IAAA,mBAAW,EAAC,SAAS,EAAE,EAAE,CAAC,EAAE;YAC7B,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;SACjF;QAED,QAAA,kBAAM;YACF,YAAY,EAAE,IAAI;YAClB,4BAA4B,EAAE,IAAI;YAClC,MAAM,EAAE,CAAC,MAAM,IAAI,CAAC,CAAC;YACrB,UAAU,EAAE,UAAU;YACtB,SAAS,EAAE,SAAS;SACvB,CAAC,SAAC;;IACP,CAAC;IACL,gCAAC;AAAD,CAAC,AApBD,CAA+C,SAAS,GAoBvD;AApBY,8DAAyB;AA0BtC,+BAA+B;AAC/B,qBAAqB;AACrB;IAgFI;;QACI,MAAM,CAAC,aAAa,aAAa,QAAQ,CAAC,CAAC;QAC3C,IAAA,2BAAc,EAAC,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;IAC9C,CAAC;IA3EK,6BAAU,GAAhB;;;;;4BACgC,qBAAM,IAAA,8BAAiB,EAAC;4BAChD,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;4BAC9B,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,UAAC,KAAK;gCACrC,+CAA+C;gCAC/C,qBAAqB;gCACrB,OAAO,IAAI,CAAC;4BAChB,CAAC,CAAC;yBACL,CAAC,EAAA;;wBAPI,KAAsB,SAO1B,EAPM,KAAK,WAAA,EAAE,QAAQ,cAAA;wBASnB,iBAAiB,GAAG,IAAI,EAAE,YAAY,GAAG,IAAI,EAAE,oBAAoB,GAAG,IAAI,CAAC;wBAE/E,IAAI,KAAK,IAAI,KAAK,CAAC,aAAa,EAAE;4BAC9B,6DAA6D;4BAC7D,wDAAwD;4BACxD,+CAA+C;4BAC/C,iBAAiB,GAAG,KAAK,CAAC,aAAa,CAAC;4BACxC,oBAAoB,GAAG,qBAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;4BACpD,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;yBACvE;wBAED,sBAAO,EAAE,iBAAiB,mBAAA,EAAE,YAAY,cAAA,EAAE,oBAAoB,sBAAA,EAAE,QAAQ,UAAA,EAAE,EAAC;;;;KAC9E;IAmCD,iBAAiB;IACjB,8BAAW,GAAX,UAAY,SAAoB,EAAE,QAAkB;QAChD,OAAO,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED,kBAAkB;IAClB,iCAAc,GAAd,UAAe,SAAoB,EAAE,QAAkB;QACnD,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC;IAYM,mBAAU,GAAjB,UAAkB,KAAU;QACxB,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;IAC1C,CAAC;IAyCL,eAAC;AAAD,CAAC,AAhID,IAgIC;AAhIqB,4BAAQ"}