# 收藏API接口文档

## 基本信息
- **Base URL**: `http://search.dinq.io/api/favorite`
- **认证方式**: Bearer <PERSON>ken
- **请求头**: `Authorization: Bearer YOUR_TOKEN`

## 接口列表

### 1. 添加收藏
将学者档案添加到收藏夹

**请求**
```
POST /api/v1/favorite/add/{profile_id}
```

**参数**
- `profile_id` (必需): UUID格式，搜索接口返回的profile.id

**示例**
```bash
curl -X POST 'http://search.dinq.io/api/favorite/api/v1/favorite/add/cc1d75b6-aadb-40d9-a8b6-ccd25f13a2be' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -d ''
```

**响应示例**
```json
{
  "data": {
    "id": "da65b678-47b7-4a0e-9dc2-5b0ecc8c1b49",
    "owner_id": "LdQge8ey7DbnRF1bOheMt4wc0Il1",
    "creator_id": null,
    "parent_id": "00000000-0000-0000-0000-000000000000",
    "is_folder": false,
    "level": 0,
    "order": 0,
    "folder_name": null,
    "created_at": "2025-07-25T05:36:37.927217",
    "updated_at": "2025-07-25T05:36:37.928053"
  }
}
```

### 2. 移除收藏
从收藏夹中删除指定项目

**请求**
```
DELETE /api/v1/favorite/remove/{favorite_id}
```

**参数**
- `favorite_id` (必需): UUID格式，收藏接口返回的favorite.id

**示例**
```bash
curl -X DELETE 'http://search.dinq.io/api/favorite/api/v1/favorite/remove/da65b678-47b7-4a0e-9dc2-5b0ecc8c1b49' \
  -H 'Authorization: Bearer YOUR_TOKEN'
```

**响应示例**
```json
{
  "data": true
}
```

### 3. 重新排序
调整收藏项目的显示顺序

**请求**
```
POST /api/v1/favorite/reorder
```

**请求体**
```json
{
  "favorite_id": "da65b678-47b7-4a0e-9dc2-5b0ecc8c1b49",
  "order": 5
}
```

**参数说明**
- `favorite_id`: UUID格式，收藏接口返回的favorite.id
- `order`: 整数，新的排序位置（≥0）

**示例**
```bash
curl -X POST 'http://search.dinq.io/api/favorite/api/v1/favorite/reorder' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{"favorite_id": "da65b678-47b7-4a0e-9dc2-5b0ecc8c1b49", "order": 5}'
```

**响应示例**
```json
{
  "data": true
}
```

### 4. 获取收藏列表
获取指定文件夹中的收藏内容

**请求**
```
GET /api/v1/favorite/list?folder_id={folder_id}&filter={filter}
```

**查询参数**
- `folder_id` (可选): UUID格式，文件夹ID。不传默认为根文件夹
- `filter` (可选): 过滤类型
  - `all` (默认): 列出所有人才卡片和文件夹
  - `folder`: 只列出文件夹

**示例**
```bash
# 获取根文件夹所有内容
curl -X GET 'http://search.dinq.io/api/favorite/api/v1/favorite/list' \
  -H 'Authorization: Bearer YOUR_TOKEN'

# 只获取文件夹
curl -X GET 'http://search.dinq.io/api/favorite/api/v1/favorite/list?filter=folder' \
  -H 'Authorization: Bearer YOUR_TOKEN'
```

**响应示例**
```json
{
  "data": [
    {
      "id": "da65b678-47b7-4a0e-9dc2-5b0ecc8c1b49",
      "owner_id": "LdQge8ey7DbnRF1bOheMt4wc0Il1",
      "is_folder": false,
      "order": 5,
      "profile": {
        "id": "cc1d75b6-aadb-40d9-a8b6-ccd25f13a2be",
        "name": "Avijit Ghosh",
        "research_areas": ["Fair and Ethical AI", "Machine Learning"],
        "scholar": "X9y2jJIAAAAJ"
      }
    }
  ]
}
```

### 5. 创建文件夹
在收藏夹中创建新文件夹

**请求**
```
POST /api/v1/favorite/folder
```

**请求体**
```json
{
  "parent_id": null
}
```

**参数说明**
- `parent_id` (可选): UUID格式，父文件夹ID。不传默认在根文件夹创建

**示例**
```bash
curl -X POST 'http://search.dinq.io/api/favorite/api/v1/favorite/folder' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{"parent_id": null}'
```

### 6. 移动到文件夹
将收藏项目或文件夹移动到指定文件夹

**请求**
```
PATCH /api/v1/favorite/folder
```

**请求体**
```json
{
  "favorite_id": "da65b678-47b7-4a0e-9dc2-5b0ecc8c1b49",
  "folder_id": "folder-uuid-here"
}
```

**参数说明**
- `favorite_id`: UUID格式，要移动的对象ID（人才卡片或文件夹）
- `folder_id` (可选): UUID格式，目标文件夹ID。不传默认移动到根文件夹

**示例**
```bash
curl -X PATCH 'http://search.dinq.io/api/favorite/api/v1/favorite/folder' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{"favorite_id": "da65b678-47b7-4a0e-9dc2-5b0ecc8c1b49", "folder_id": null}'
```

### 7. 删除文件夹
删除指定文件夹（必须为空）

**请求**
```
DELETE /api/v1/favorite/folder/{folder_id}
```

**参数**
- `folder_id` (必需): UUID格式，要删除的文件夹ID

**注意**: 文件夹必须为空才能删除，否则返回400错误

**示例**
```bash
curl -X DELETE 'http://search.dinq.io/api/favorite/api/v1/favorite/folder/folder-uuid-here' \
  -H 'Authorization: Bearer YOUR_TOKEN'
```

### 8. 重命名文件夹
修改文件夹名称

**请求**
```
POST /api/v1/favorite/folder/rename
```

**请求体**
```json
{
  "folder_id": "folder-uuid-here",
  "name": "新文件夹名称"
}
```

**参数说明**
- `folder_id`: UUID格式，要重命名的文件夹ID
- `name`: 字符串，新的文件夹名称（1-32个字符，可重名）

**示例**
```bash
curl -X POST 'http://search.dinq.io/api/favorite/api/v1/favorite/folder/rename' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{"folder_id": "folder-uuid-here", "name": "AI研究者"}'
```

## 数据结构说明

### TalentFavorite 对象
```json
{
  "id": "string(uuid)",           // 收藏项目ID
  "owner_id": "string",           // 所有者ID
  "creator_id": "string|null",    // 创建者ID
  "parent_id": "string(uuid)",    // 父文件夹ID
  "is_folder": "boolean",         // 是否为文件夹
  "level": "integer",             // 层级
  "order": "integer",             // 排序
  "folder_name": "string|null",   // 文件夹名称
  "created_at": "datetime",       // 创建时间
  "updated_at": "datetime"        // 更新时间
}
```

## 错误处理
- **401**: 认证失败，检查Authorization头
- **400**: 请求参数错误（如删除非空文件夹）
- **404**: 资源不存在
- **422**: 参数验证错误
- **500**: 服务器内部错误

## 使用注意事项
1. 所有请求都需要Authorization头
2. UUID参数必须是有效的UUID格式
3. 文件夹名称长度限制在1-32个字符
4. 删除文件夹前需确保文件夹为空
5. 排序order值必须≥0
6. 根文件夹的parent_id为"00000000-0000-0000-0000-000000000000"